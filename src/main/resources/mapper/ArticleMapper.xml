<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mill.tea.political.mapper.ArticleMapper">

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO article (url, name, text, process_rule)
        VALUES
        <foreach collection="articles" item="item" separator=",">
            (#{item.url}, #{item.name}, #{item.text}, #{item.processRule,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler})
        </foreach>
    </insert>
</mapper>