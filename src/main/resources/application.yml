spring:
  main:
    #是否允许循环依赖
    allow-circular-references: true
    allow-bean-definition-overriding: true

  datasource:
    url: *******************************************************************************************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      read-only: false
      #客户端等待连接池连接的最大毫秒数
      connection-timeout: 60000
      #允许连接在连接池中空闲的最长时间(以毫秒为单位)
      # idle-timeout: 60000
      #连接将被测试活动的最大时间量
      validation-timeout: 60000
      #池中连接关闭后的最长生命周期
      max-lifetime: 1800000
      #最大池大小
      maximum-pool-size: 144
      #连接池中维护的最小空闲连接数
      minimum-idle: 40
      #从池返回的连接的默认自动提交行为。默认值为true
      auto-commit: true
      #如果您的驱动程序支持JDBC4，我们强烈建议您不要设置此属性
      # connection-test-query: SELECT 1
      #自定义连接池名称
      pool-name: MyHikariCP

mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml