package cn.mill.tea.kjtb;

import cn.hutool.core.bean.BeanUtil;
import cn.mill.tea.kjtb.entity.EducationExport;
import cn.mill.tea.kjtb.entity.Work;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import lombok.SneakyThrows;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class ExpertWork {
    @SneakyThrows
    public static void main(String[] args) {
        List<Object> sheet1 = EasyExcel.read(new File("C:\\Users\\<USER>\\Desktop\\工作经历-第八批.xlsx"), Work.class,null).sheet("Sheet1").doReadSync();
        List<Work> educations = BeanUtil.copyToList(sheet1, Work.class);
        MultiValuedMap<String,Work> map = new ArrayListValuedHashMap<>();
        for (Work education : educations) {
            if (education.getPhone()==null){
                education.setPhone("");
            }
            String key = education.getName()+"_"+education.getPhone();
            map.put(key,education);
        }

        List<EducationExport> export = new ArrayList<>();
        for (String key : map.keySet()) {
            List<Work> list = (List<Work>)map.get(key);
            EducationExport e = new EducationExport();
            e.setName_phone(key);
            e.setEducation_intro(JSON.toJSONString(list));
            export.add(e);
        }

        EasyExcel.write(new File("C:\\Users\\<USER>\\Desktop\\工作经历-第八批-聚合.xlsx")).sheet("Sheet1").doWrite(export);

    }
}