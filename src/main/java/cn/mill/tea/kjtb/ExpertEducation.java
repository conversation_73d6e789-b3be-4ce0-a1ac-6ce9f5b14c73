package cn.mill.tea.kjtb;

import cn.hutool.core.bean.BeanUtil;
import cn.mill.tea.kjtb.entity.Education;
import cn.mill.tea.kjtb.entity.EducationExport;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import lombok.SneakyThrows;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class ExpertEducation {

    @SneakyThrows
    public static void main(String[] args) {
        List<Object> sheet1 = EasyExcel.read(new File("C:\\Users\\<USER>\\Desktop\\教育背景-第八批.xlsx"), Education.class,null).sheet("Sheet1").doReadSync();
        List<Education> educations = BeanUtil.copyToList(sheet1, Education.class);
        MultiValuedMap<String,Education> map = new ArrayListValuedHashMap<>();
        for (Education education : educations) {
            if (education.getPhone()==null){
                education.setPhone("");
            }
            String key = education.getName()+"_"+education.getPhone();
            map.put(key,education);
        }

        List<EducationExport> export = new ArrayList<>();
        for (String key : map.keySet()) {
            List<Education> list = (List<Education>)map.get(key);
            EducationExport e = new EducationExport();
            e.setName_phone(key);
            e.setEducation_intro(JSON.toJSONString(list));
            export.add(e);
        }

        EasyExcel.write(new File("C:\\Users\\<USER>\\Desktop\\教育背景-第八批-聚合.xlsx")).sheet("Sheet1").doWrite(export);

    }
}