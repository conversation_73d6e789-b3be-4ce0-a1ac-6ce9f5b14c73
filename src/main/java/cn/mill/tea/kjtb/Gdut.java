package cn.mill.tea.kjtb;

import com.alibaba.fastjson2.JSON;
import lombok.SneakyThrows;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class Gdut {
    @SneakyThrows
    public static void main(String[] args) {
//        List<String> list  = new ArrayList<>();
//        for (String url : list){
//            Document document = Jsoup.connect(url).get();
//            String html = document.html().replace(" ","").replace("\\s","")
//                    .replace("<spanstyle=\\\"font-weight:bold;\\\">","")
//                    .replace("<\\/span>","");
//            String regex = "varu_u8_picurl=(.*?);";
//            Pattern compile = Pattern.compile(regex);
//            Matcher matcher = compile.matcher(html);
//            matcher.find();
//            String group = matcher.group(1);
//            List<String> imgs = JSON.parseArray(group, String.class);
//            List<String> imgUrls = imgs.stream().map(e -> "https://yzw.gdut.edu.cn" + e).collect(Collectors.toList());
//            String regexName = "varu_u8_title=(.*?);";
//            Pattern compileName = Pattern.compile(regexName);
//            Matcher matcherName = compileName.matcher(html);
//            matcherName.find();
//            String groupName = matcherName.group(1);
//            try{
//                List<String> names = JSON.parseArray(groupName, String.class);
//                for (int i =0;i<names.size();i++){
//                    System.out.println(names.get(i)+"\t"+imgUrls.get(i));
//                }
//            }catch (Exception e){
//                System.out.println("出错啦："+groupName);
//                throw e;
//            }
//
//        }

        avatar();
    }

    public static void avatar(){
        List<String> list = new ArrayList<>();

        for (String s : list) {
            System.out.printf("[{\"bucketName\": \"jvs-form-design\",\"fileName\": \"%s\",\"url\": \"http://ggkjtb-oss.mtu.plus/jvs-form-design/%s\"}]%n",s,s);
        }
    }
}