package cn.mill.tea.kjtb;

import cn.hutool.core.bean.BeanUtil;
import cn.mill.tea.kjtb.entity.Shop;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import org.jsoup.Jsoup;

import java.util.List;

public class UpdateRichText {

    public static void main(String[] args) {
        ExcelReaderBuilder read = EasyExcel.read("C:\\Users\\<USER>\\Desktop\\444d5f2900335912bd06c3916966903678f219.xlsx", Shop.class, null);
        List<Object> objects = read.sheet().doReadSync();
        List<Shop> shops = BeanUtil.copyToList(objects, Shop.class);
        shops.forEach(e->{
            if (e.getCorp_intro()!=null){
                e.setCorp_intro(Jsoup.parse(e.getCorp_intro()).wholeText());
            }
            if (e.getIntro()!=null){
                e.setIntro(Jsoup.parse(e.getIntro()).wholeText());
            }
            if (e.getOrga_intro()!=null){
                e.setOrga_intro(Jsoup.parse(e.getOrga_intro()).wholeText());
            }
        });
        EasyExcel.write("C:\\Users\\<USER>\\Desktop\\444d5f2900335912bd06c3916966903678f219-new.xlsx").sheet("Sheet1").doWrite(shops);
    }

}