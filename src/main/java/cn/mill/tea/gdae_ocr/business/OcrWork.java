package cn.mill.tea.gdae_ocr.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.mill.tea.gdae_ocr.entity.ActivityResign;
import cn.mill.tea.gdae_ocr.entity.Leader;
import cn.mill.tea.gdae_ocr.entity.LeaderExport;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class OcrWork {

    public static void main(String[] args) {
        jiaoxue();

//        jiaoyan();
    }

    public static void jiaoxue() {
        File jsonFile = new File("C:\\Users\\<USER>\\Desktop\\jiaoxue.json");
        byte[] bytes = IoUtil.readBytes(FileUtil.getInputStream(jsonFile));
        String s = new String(bytes);
        List<ActivityResign> resigns = JSON.parseArray(s, ActivityResign.class);
        List<LeaderExport> list = new ArrayList<>();

        for (ActivityResign resign : resigns) {
            List<Leader> leaders = resign.getLeaders();
            LeaderExport leaderExport = new LeaderExport();

            if (CollUtil.isEmpty(leaders)) {
                log.error("{} ---leaders is empty", resign.getProject_name());
                continue;
            }

            leaderExport.setProjectName(resign.getProject_name());
            leaderExport.setLeaderName(leaders.get(0).getName());
            leaderExport.setLeaderPhone(leaders.get(0).getPhone());

            int size = leaders.size();
            if (size > 3) {
                log.info("{} ---leaders > 3", resign.getProject_name());
            }
            if (size >= 2) {
                leaderExport.setLeader2Name(leaders.get(1).getName());
                leaderExport.setLeader2Phone(leaders.get(1).getPhone());
            }

            if (size == 3) {
                leaderExport.setLeader3Name(leaders.get(2).getName());
                leaderExport.setLeader3Phone(leaders.get(2).getPhone());
            }

            list.add(leaderExport);

        }

        EasyExcel.write(new File("C:\\Users\\<USER>\\Desktop\\jiaoxue.xlsx")).sheet("Sheet1").doWrite(list);
    }

    public static void jiaoyan() {
        File jsonFile = new File("C:\\Users\\<USER>\\Desktop\\jiaoyan.json");
        byte[] bytes = IoUtil.readBytes(FileUtil.getInputStream(jsonFile));
        String s = new String(bytes);
        List<ActivityResign> resigns = JSON.parseArray(s, ActivityResign.class);
        List<LeaderExport> list = new ArrayList<>();

        for (ActivityResign resign : resigns) {
            List<Leader> leaders = resign.getLeaders();
            LeaderExport leaderExport = new LeaderExport();

            if (CollUtil.isEmpty(leaders)) {
                log.error("{} ---leaders is empty", resign.getProject_name());
                continue;
            }

            leaderExport.setProjectName(resign.getProject_name());
            leaderExport.setLeaderName(leaders.get(0).getName());
            leaderExport.setLeaderPhone(leaders.get(0).getPhone());

            int size = leaders.size();
            if (size > 3) {
                log.info("{} ---leaders > 3", resign.getProject_name());
            }
            if (size >= 2) {
                leaderExport.setLeader2Name(leaders.get(1).getName());
                leaderExport.setLeader2Phone(leaders.get(1).getPhone());
            }

            if (size == 3) {
                leaderExport.setLeader3Name(leaders.get(2).getName());
                leaderExport.setLeader3Phone(leaders.get(2).getPhone());
            }

            list.add(leaderExport);

        }

        EasyExcel.write(new File("C:\\Users\\<USER>\\Desktop\\jiaoyan.xlsx")).sheet("Sheet1").doWrite(list);
    }
}