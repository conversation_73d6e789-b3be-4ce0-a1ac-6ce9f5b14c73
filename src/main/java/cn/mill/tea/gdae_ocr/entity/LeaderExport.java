package cn.mill.tea.gdae_ocr.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class LeaderExport {

    @ExcelProperty("leader_name")
    private String leaderName;

    @ExcelProperty("leader_phone")
    private String leaderPhone;

    @ExcelProperty("leader2_name")
    private String leader2Name;

    @ExcelProperty("leader2_phone")
    private String leader2Phone;

    @ExcelProperty("leader3_name")
    private String leader3Name;

    @ExcelProperty("leader3_phone")
    private String leader3Phone;

    @ExcelProperty("project_name")
    private String projectName;

}