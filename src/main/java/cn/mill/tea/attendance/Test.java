package cn.mill.tea.attendance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class Test {

    @Data
    public static class Param {
        private List<String> leaveTime;
        private Double leaveDuration;
        private List<LeaveAndFlexible> leaveAndFlexible;
    }

    @Data
    public static class LeaveAndFlexible {
        private Double leaveDuration;
        private String staffName;
        private String staffNumber;
        private List<String> time;
    }

    public static void main(String[] args) {
        String a = "{\n" +
                "  \"leaveTime\": [\n" +
                "    \"2025-09-15 08:00:00\",\n" +
                "    \"2025-09-15 12:00:00\"\n" +
                "  ],\n" +
                "  \"leaveDuration\": 0.5,\n" +
                "  \"leaveAndFlexible\": [\n" +
                "    {\n" +
                "      \"leaveDuration\": 0.5,\n" +
                "      \"staffName\": \"张老师\",\n" +
                "      \"staffNumber\": \"125678\",\n" +
                "      \"time\": [\n" +
                "        \"2025-09-13 14:00:00\",\n" +
                "        \"2025-09-14 12:00:00\"\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"leaveDuration\": 0.5,\n" +
                "      \"staffName\": \"张老师\",\n" +
                "      \"staffNumber\": \"125678\",\n" +
                "      \"time\": [\n" +
                "        \"2025-09-14 14:00:00\",\n" +
                "        \"2025-09-14 17:00:00\"\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"leaveDuration\": 0.5,\n" +
                "      \"staffName\": \"张老师\",\n" +
                "      \"staffNumber\": \"125678\",\n" +
                "      \"time\": [\n" +
                "        \"2025-09-16 08:00:00\",\n" +
                "        \"2025-09-17 08:00:00\"\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        String b = "" +
                "{\n" +
                "  \"leaveTime\": [\n" +
                "    \"2025-09-15 08:00:00\",\n" +
                "    \"2025-09-15 17:00:00\"\n" +
                "  ],\n" +
                "  \"leaveDuration\": 1,\n" +
                "  \"leaveAndFlexible\": [\n" +
                "    {\n" +
                "      \"leaveDuration\": 0.5,\n" +
                "      \"staffName\": \"张老师\",\n" +
                "      \"staffNumber\": \"125678\",\n" +
                "      \"time\": [\n" +
                "        \"2025-09-13 14:00:00\",\n" +
                "        \"2025-09-14 12:00:00\"\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"leaveDuration\": 0.5,\n" +
                "      \"staffName\": \"张老师\",\n" +
                "      \"staffNumber\": \"125678\",\n" +
                "      \"time\": [\n" +
                "        \"2025-09-14 14:00:00\",\n" +
                "        \"2025-09-14 17:00:00\"\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"leaveDuration\": 0.5,\n" +
                "      \"staffName\": \"张老师\",\n" +
                "      \"staffNumber\": \"125678\",\n" +
                "      \"time\": [\n" +
                "        \"2025-09-16 08:00:00\",\n" +
                "        \"2025-09-17 08:00:00\"\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        Map param = JSONObject.parseObject(b, Map.class);
        List<LeaveAndFlexible> result = getLeaveAndFlexibles(param);
        System.out.println(result);

    }

    private static List<LeaveAndFlexible> getLeaveAndFlexibles(Map map) {
        Param param = JSONObject.parseObject(JSON.toJSONString(map), Param.class);
        List<String> leaveTime = param.getLeaveTime();
        String start = leaveTime.get(0);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTimeUtil.parse(start, formatter);
        Double leaveDuration = param.getLeaveDuration();
        List<LeaveAndFlexible> leaves = param.getLeaveAndFlexible();
        //先分开时间
        List<LeaveAndFlexible> beforeList = leaves.stream().filter(e -> {
            String time = e.getTime().get(0);
            LocalDateTime localDateTime = LocalDateTimeUtil.parse(time, formatter);
            return localDateTime.isBefore(startTime);
        }).collect(Collectors.toList());
        beforeList = CollUtil.reverse(beforeList);

        List<LeaveAndFlexible> afterList = leaves.stream().filter(e -> {
            String time = e.getTime().get(0);
            LocalDateTime localDateTime = LocalDateTimeUtil.parse(time, formatter);
            return localDateTime.isAfter(startTime);
        }).collect(Collectors.toList());

        //分两种情况，1；当前请了0.5天  2：当前请1天
        //1: 当前请0.5天
        //判断前面的时间
        //判断前后相连的时间
        //判断后面的时间

        List<LeaveAndFlexible> result = new ArrayList<>();
        if (leaveDuration == 0.5) {
            //如果请假是上午
            if (startTime.getHour() < 12) {
                boolean changeBeforeFlag = false;
                for (LeaveAndFlexible leave : beforeList) {
                    LocalDateTime endTime = LocalDateTimeUtil.parse(leave.getTime().get(1), formatter);
                    if (!changeBeforeFlag && endTime.getHour() >= 14) {
                        changeBeforeFlag = true;
                        result.add(leave);
                    }

                    if (changeBeforeFlag && endTime.getHour() <= 12) {
                        result.add(leave);
                    }

                    //如果没有第一个匹配，则直接跳过
                    if (!changeBeforeFlag) {
                        break;
                    }

                    //转回
                    result = CollUtil.reverse(result);
                }

                boolean changeAfterFlag = false;
                for (LeaveAndFlexible leave : afterList) {
                    LocalDateTime beginTime = LocalDateTimeUtil.parse(leave.getTime().get(0), formatter);
                    if (!changeAfterFlag && beginTime.getHour() >= 14) {
                        changeAfterFlag = true;
                        result.add(leave);
                    }

                    if (changeAfterFlag && beginTime.getHour() <= 12) {
                        changeAfterFlag = false;
                        result.add(leave);
                    }

                    //如果没有第一个匹配，则直接跳过
                    if (!changeAfterFlag) {
                        break;
                    }
                }
            }
            if (startTime.getHour() > 12) {
                boolean changeBeforeFlag = false;
                for (LeaveAndFlexible leave : beforeList) {
                    LocalDateTime endTime = LocalDateTimeUtil.parse(leave.getTime().get(1), formatter);
                    if (!changeBeforeFlag && endTime.getHour() <= 12) {
                        changeBeforeFlag = true;
                        result.add(leave);
                    }

                    if (changeBeforeFlag && endTime.getHour() >= 14) {
                        result.add(leave);
                    }

                    //如果没有第一个匹配，则直接跳过
                    if (!changeBeforeFlag) {
                        break;
                    }

                    //转回
                    result = CollUtil.reverse(result);
                }

                boolean changeAfterFlag = false;
                for (LeaveAndFlexible leave : afterList) {
                    LocalDateTime beginTime = LocalDateTimeUtil.parse(leave.getTime().get(0), formatter);
                    if (!changeAfterFlag && beginTime.getHour() <= 12) {
                        changeAfterFlag = true;
                        result.add(leave);
                    }

                    if (changeAfterFlag && beginTime.getHour() >= 14) {
                        result.add(leave);
                    }

                    if (!changeAfterFlag) {
                        break;
                    }
                }
            }
        } else {
            //请假为1天的
            boolean changeBeforeFlag = false;
            for (LeaveAndFlexible leave : beforeList) {
                LocalDateTime endTime = LocalDateTimeUtil.parse(leave.getTime().get(1), formatter);
                if (!changeBeforeFlag && endTime.getHour() >= 14) {
                    changeBeforeFlag = true;
                    result.add(leave);
                }

                if (changeBeforeFlag && endTime.getHour() <= 12) {
                    result.add(leave);
                }

                //如果没有第一个匹配，则直接跳过
                if (!changeBeforeFlag) {
                    break;
                }

                //转回
                result = CollUtil.reverse(result);
            }

            boolean changeAfterFlag = false;
            for (LeaveAndFlexible leave : afterList) {
                LocalDateTime beginTime = LocalDateTimeUtil.parse(leave.getTime().get(0), formatter);
                if (!changeAfterFlag && beginTime.getHour() <= 12) {
                    changeAfterFlag = true;
                    result.add(leave);
                }

                if (changeAfterFlag && beginTime.getHour() >= 14) {
                    result.add(leave);
                }

                if (!changeAfterFlag) {
                    break;
                }
            }
        }
        Double total = result.stream().map(LeaveAndFlexible::getLeaveDuration).reduce(Double::sum).orElse(0.0);
        if (leaveDuration + total <= 1) {
            return new ArrayList<>();
        }
        return result;
    }
}