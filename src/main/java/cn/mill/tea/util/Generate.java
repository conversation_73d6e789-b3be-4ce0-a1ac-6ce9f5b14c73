package cn.mill.tea.util;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.util.Arrays;

public class Generate {
    private static final String url = "**********************************************" +
            "?useUnicode=true&characterEncoding=utf8" +
            "&allowPublicKeyRetrieval=True&useSSL=false" +
            "&serverTimezone=Asia/Shanghai";
    private static final String userName = "root";
    private static final String password = "8xWliroE";
    private static final String[] tableName = new String[]{
            "ai_knowledge_item","ai_item_segments"
    };
    private static final String tablePrefix = "ai_";

    public static void main(String[] args) {
        AutoGenerator autoGenerator = new AutoGenerator();

        // 全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        String projectPath = System.getProperty("user.dir");
        globalConfig.setOutputDir(projectPath + "/jvs-apply-document/document-common" + "/src/main/java");
        globalConfig.setAuthor("Aaron2");
        globalConfig.setFileOverride(false);
        globalConfig.setOpen(false);
        // mapper.xml 生成 ResultMap
        globalConfig.setBaseResultMap(true);
        // mapper.xml 生成 ColumnList
        globalConfig.setBaseColumnList(true);

        globalConfig.setMapperName("%sMapper");
        // gc.setXmlName("%sDao");
        globalConfig.setServiceName("%sService");
        globalConfig.setServiceImplName("%sServiceImpl");
        globalConfig.setControllerName("%sController");
        globalConfig.setSwagger2(true);
        autoGenerator.setGlobalConfig(globalConfig);

        // 数据源配置
        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setUrl(url);
        // dsc.setSchemaName("public");
        dataSourceConfig.setDriverName("com.mysql.cj.jdbc.Driver");
        dataSourceConfig.setUsername(userName);
        dataSourceConfig.setPassword(password);
        autoGenerator.setDataSource(dataSourceConfig);

        // 包配置
        PackageConfig packageConfig = new PackageConfig();
        // pc.setModuleName("xxx");
        packageConfig.setParent("cn.bctools.ai.knowledge");
        packageConfig.setEntity("entity.data");
//        packageConfig.setParent("cn.bctools.custom." + tableName.substring(tablePrefix.length()));
        autoGenerator.setPackageInfo(packageConfig);

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // to do nothing
            }
        };

        // 配置自定义输出模板
        TemplateConfig templateConfig = new TemplateConfig();
        //指定自定义模板路径，注意不要带上.ftl/.vm, 会根据使用的模板引擎自动识别
        templateConfig.setEntity("templates_mp/entity.java");
        templateConfig.setController("templates_mp/controller.java");
        templateConfig.setServiceImpl("templates_mp/serviceImpl.java");
        templateConfig.setMapper("templates_mp/mapper.java");
        autoGenerator.setTemplate(templateConfig);

        // 策略配置
        StrategyConfig strategyConfig = new StrategyConfig();
        strategyConfig.setNaming(NamingStrategy.underline_to_camel);
        strategyConfig.setColumnNaming(NamingStrategy.underline_to_camel);
        strategyConfig.setEntityLombokModel(true);
        strategyConfig.setRestControllerStyle(true);
        strategyConfig.setChainModel(true);
        strategyConfig.setLogicDeleteFieldName("del_flag");
        strategyConfig.setEntityTableFieldAnnotationEnable(true);
        strategyConfig.setTableFillList(Arrays.asList(
                new TableFill("create_time", FieldFill.INSERT),
                new TableFill("create_by", FieldFill.INSERT),
                new TableFill("create_by_id", FieldFill.INSERT),
                new TableFill("update_time", FieldFill.INSERT_UPDATE),
                new TableFill("update_by", FieldFill.INSERT_UPDATE),
                new TableFill("update_by_id", FieldFill.INSERT_UPDATE)
        ));

        // 表名
        strategyConfig.setInclude(tableName);
        strategyConfig.setTablePrefix(tablePrefix);
        autoGenerator.setStrategy(strategyConfig);
        strategyConfig.setControllerMappingHyphenStyle(true);
        autoGenerator.setTemplateEngine(new FreemarkerTemplateEngine());
        autoGenerator.execute();
    }
}