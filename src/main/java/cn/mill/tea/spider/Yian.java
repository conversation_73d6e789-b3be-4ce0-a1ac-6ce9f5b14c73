package cn.mill.tea.spider;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public class Yian {

    private static final String DATABASE = "医案库";
    private static final String baseUrl = "http://tcm.sstp.cn/";

    @SneakyThrows
    public static void main(String[] args) {
        String baseUrl = "http://tcm.sstp.cn/";
        String cataUrl = "http://tcm.sstp.cn/index.php?categorylist#yian";
        Document cata = Jsoup.connect(cataUrl).get();
        Elements links = cata.select("ul.yian > li > a");
        List<BasicInfo> cataData = new ArrayList<>();
        List<BasicInfo> result = new ArrayList<>();
        // 处理每个医案大分类  内科疾病|外科疾病。。。
        for (Element link : links) {
            String href = link.attr("href");

            if (!href.isEmpty()) {
                BasicInfo data = new BasicInfo();
                data.setUrl(baseUrl + href);
                cataData.add(data);
            }
        }

        List<BasicInfo> subTypes = new ArrayList<>();
        subTypes.addAll(processCataData(cataData));

        List<BasicInfo> subPages = new ArrayList<>();
        for (BasicInfo subType : subTypes) {
            Document subInfo = getDocument(subType.getUrl());

            Elements subs = subInfo.select("dl.i6-ff > dt > a");

            // 可能没有子项了，直接处理分页-详细数据
            List<Integer> list = handleFenYeCount(subInfo);

            result.addAll(processListData(subType, list));

            // 记录子分类
            for (Element sub : subs) {
                String href = sub.attr("href");
                String text = sub.text();
                if (href.isEmpty() || text.isEmpty()) {
                    continue;
                }
                BasicInfo subPage = new BasicInfo();
                subPage.setUrl(baseUrl + href);
                subPage.setName(text);
                subPage.setMedType(subType.getMedType());
                subPage.setBigType(subType.getBigType());
                subPages.add(subPage);
                log.info(subPage.toString());
                System.out.println(subPage);
            }
        }


        //处理小分类子分类
        for (BasicInfo subPage : subPages) {
            Document subInfo = getDocument(subPage.getUrl());

            // 可能没有子项了，直接处理分页-详细数据
            List<Integer> list = handleFenYeCount(subInfo);

            result.addAll(processListData(subPage, list));
        }
        // 处理每个医案小分类 内科疾病-热病 内科疾病-肺系疾病。。。

        // 处理详细信息

        String[] headers = {"库分类", "疾病分类", "疾病类型", "具体疾病或证型", "词条标题", "药物描述", "患者信息"};

        // 创建 CSV 格式化器
        try (BufferedWriter writer = new BufferedWriter(new FileWriter("医案库.csv"));
             CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(headers))) {

            fetchInfo(result, csvPrinter);

            System.out.println("CSV 文件已生成: " + "医案库.csv");
        }

//        writeCsv(yiAnCsvBos, "医案库.csv");


        System.out.println(1);
    }

    @SneakyThrows
    private static Document getDocument(String url) {
        return Jsoup.connect(url)
                .cookie("hd_sid", AuthConstant.HD_SID)
                .cookie("hd_auth", AuthConstant.HD_AUTH)
                .get();
    }


    @SneakyThrows
    public static void writeCsv(List<YiAnCsvBo> dataList, String fileName) {
        // 定义 CSV 文件的列头
        String[] headers = {"库分类", "疾病分类", "疾病类型", "具体疾病或证型", "词条标题", "药物描述", "患者信息"};

        // 创建 CSV 格式化器
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(fileName));
             CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(headers))) {

            // 循环遍历 CsvBo 列表，将每个对象的数据写入 CSV 文件
            for (YiAnCsvBo csvBo : dataList) {
                csvPrinter.printRecord(DATABASE, csvBo.getBigType(), csvBo.getMedType(), csvBo.getMedName(), csvBo.getName(), csvBo.getContent(), csvBo.getPatientInfo());
            }

            System.out.println("CSV 文件已生成: " + fileName);
        }
    }

    @SneakyThrows
    public static List<YiAnCsvBo> fetchInfo(List<BasicInfo> infoList, CSVPrinter csvPrinter) {
        // 创建线程池，数量为可用核心数
        int threadCount = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

        // 用于存储所有的 Future 对象
        List<Future> futures = new ArrayList<>();

        // 使用线程池异步处理 infoList 中的每个 BasicInfo 对象
        for (BasicInfo info : infoList) {
            // 提交每个任务到线程池
            futures.add(executorService.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        String infoUrl = info.getUrl();
                        Document infoDocument = getDocument(infoUrl);
//                    String patientInfo = handlePatientInfo(infoDocument.select("div.w-950 > div.w-950.bg_white.zoom > div.l.w-710.o-v > div.content_aoption > ul > li"));
                        String patientInfo = Optional.ofNullable(infoDocument.select("div.w-950 > div.w-950.bg_white.zoom > div.l.w-710.o-v > div.content_aoption > ul > li"))
                                .map(Elements::text).orElse("");
                        String content = Optional.of(infoDocument)
                                .map(e -> e.select("div.w-950 > div.w-950.bg_white.zoom > div.l.w-710.o-v > div.content_1.wordcut > div.content_topp"))
                                .map(e -> e.text().replaceAll("sstp上海科学技术出版社nihao", "")).orElse("");

                        YiAnCsvBo csvBo = new YiAnCsvBo();
                        csvBo.setDatabase(DATABASE);
                        csvBo.setName(info.getName());
                        csvBo.setBigType(info.getBigType());
                        csvBo.setMedType(info.getMedType());
                        csvBo.setMedName(info.getName());
                        csvBo.setContent(content);
                        csvBo.setPatientInfo(patientInfo);
                        log.info(csvBo.toString());
                        System.out.println(csvBo);
                        try {
                            csvPrinter.printRecord(DATABASE, csvBo.getBigType(), csvBo.getMedType(), csvBo.getMedName(), csvBo.getName(), csvBo.getContent(), csvBo.getPatientInfo());
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
//                    return csvBo;
                }
            }));
        }

        List<YiAnCsvBo> resultList = new ArrayList<>();
        for (Future<YiAnCsvBo> future : futures) {
            future.get();
//            YiAnCsvBo csvBo = future.get();
//            resultList.add(csvBo);
        }

        executorService.shutdown();

        return resultList;
    }

    private static List<Integer> handleFenYeCount(Document document) {
        String maxPage;
        String fenye = Optional.ofNullable(document.getElementById("fenye"))
                .map(element -> element.select("a"))
                .map(Elements::last)
                .map(Element::text)
                .orElse(null);
        if (fenye == null) {
            maxPage = "1";
        } else {
            if (fenye.contains("...")) {
                maxPage = fenye.replace("... ", "");
            } else {
                Elements fenyeElement = document.getElementById("fenye").select("a");
                maxPage = fenyeElement.get(fenyeElement.size() - 2).text();
            }
        }
        return IntStream.rangeClosed(1, Integer.parseInt(maxPage)).boxed().collect(Collectors.toList());
    }


    @SneakyThrows
    public static List<BasicInfo> processCataData(List<BasicInfo> cataData) {
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        List<Future<List<BasicInfo>>> futures = new ArrayList<>();
        List<BasicInfo> subTypes = Collections.synchronizedList(new ArrayList<>());

        for (BasicInfo data : cataData) {
            futures.add(executorService.submit(() -> {
                List<BasicInfo> localSubTypes = new ArrayList<>();

                try {
                    String url = data.getUrl();
                    Document sideDocument = getDocument(url);

                    // 获取子类元素
                    Elements subs = sideDocument.select("dl.i6-ff > dt > a");
                    String medType = sideDocument.select("h2.col-h2").get(1).text();
                    for (Element sub : subs) {
                        BasicInfo subType = new BasicInfo();
                        subType.setUrl(baseUrl + sub.attr("href"));
                        subType.setMedType(sub.text());
                        subType.setBigType(medType);
                        localSubTypes.add(subType);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }

                return localSubTypes;
            }));
        }

        for (Future<List<BasicInfo>> future : futures) {
            subTypes.addAll(future.get());
        }

        executorService.shutdown();

        return subTypes;
    }

    @SneakyThrows
    public static List<BasicInfo> processListData(BasicInfo subType, List<Integer> list) {
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        List<Future<List<BasicInfo>>> futures = new ArrayList<>();
        List<BasicInfo> result = Collections.synchronizedList(new ArrayList<>());

        for (int i : list) {
            final int index = i;
            futures.add(executorService.submit(() -> {
                List<BasicInfo> localResult = new ArrayList<>();

                try {
                    String listUrl = subType.getUrl() + "-" + index;
                    Document listDocument = getDocument(listUrl);
                    Elements select = listDocument.select("dt.h2 > a");
                    for (Element element : select) {
                        String href = element.attr("href");
                        String text = element.text();
                        if (!href.isEmpty() && !text.isEmpty()) {
                            BasicInfo info = new BasicInfo();
                            info.setUrl(baseUrl + href);
                            info.setName(text);
                            info.setMedType(subType.getMedType());
                            info.setBigType(subType.getBigType());
                            info.setMedName(subType.getMedName());
                            localResult.add(info);
                            log.info(info.toString());
                            System.out.println(info);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }

                return localResult;
            }));
        }

        for (Future<List<BasicInfo>> future : futures) {
            result.addAll(future.get());
        }

        executorService.shutdown();

        return result;
    }

    private static String handlePatientInfo(Elements elements) {
        StrBuilder sb = new StrBuilder();
        elements.parallelStream()
                .map(e -> Optional.of(e.select("span.info-value")).map(Elements::text).orElse(""))
                .filter(StrUtil::isNotEmpty)
                .forEach(sb::append);
        return sb.toString();
    }


}
