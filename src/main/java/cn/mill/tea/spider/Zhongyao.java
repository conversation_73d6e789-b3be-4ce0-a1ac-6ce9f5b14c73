package cn.mill.tea.spider;

import lombok.SneakyThrows;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class Zhongyao {
    private static final String DATABASE = "中药库";

    @SneakyThrows
    public static void main(String[] args) {
        String baseUrl = "http://tcm.sstp.cn/";
        String cataUrl = "http://tcm.sstp.cn/index.php?categorylist#zhongyao";
        Document cata = Jsoup.connect(cataUrl).get();
        Elements links = cata.select("ul.zhongyao > li > a");
        List<BasicInfo> cataData = new ArrayList<>();
        for (Element link : links) {
            String href = link.attr("href");
            String text = link.text();

            if (!href.isEmpty() && !text.isEmpty()) {
                BasicInfo data = new BasicInfo();
                data.setUrl(baseUrl + href);
                data.setName(text);
                cataData.add(data);
            }
        }

        int startPage = 1;
        List<CsvBo> result = new ArrayList<>();
        for (BasicInfo data : cataData) {
            String url = data.getUrl();
            Document document = Jsoup.connect(url).get();
            String maxPage;
            String fenye = Optional.ofNullable(document.getElementById("fenye"))
                    .map(element -> element.select("a"))
                    .map(Elements::last)
                    .map(Element::text)
                    .orElse(null);
            if (fenye == null) {
                maxPage = "1";
            } else {
                if (fenye.contains("...")) {
                    maxPage = fenye.replace("... ", "");
                } else {
                    Elements fenyeElement = document.getElementById("fenye").select("a");
                    maxPage = fenyeElement.get(fenyeElement.size() - 2).text();
                }
            }

            // 页数
            List<Integer> list = IntStream.rangeClosed(startPage, Integer.parseInt(maxPage)).boxed().collect(Collectors.toList());
            // 详细信息
            List<BasicInfo> infoList = new ArrayList<>();
            for (int i : list) {
                String listUrl = data.getUrl() + "-" + i;
                Document listDocument = Jsoup.connect(listUrl).get();
                Elements select = listDocument.select("dt.h2 > a");
                for (Element element : select) {
                    String href = element.attr("href");
                    String text = element.text();
                    if (!href.isEmpty() && !text.isEmpty()) {
                        BasicInfo info = new BasicInfo();
                        info.setUrl(baseUrl + href);
                        info.setName(text);
                        infoList.add(info);
                    }
                }
            }

            // 开始处理所有该中药数据
            result.addAll(fetchInfo(infoList,data.getName()));

        }

        writeCsv(result, "中药库.csv");


        System.out.println(1);
    }

    @SneakyThrows
    private static Document getDocument(String url) {
        return Jsoup.connect(url)
                .cookie("hd_sid", AuthConstant.HD_SID)
                .cookie("hd_auth", AuthConstant.HD_AUTH)
                .get();
    }


    @SneakyThrows
    public static void writeCsv(List<CsvBo> dataList, String fileName) {
        // 定义 CSV 文件的列头
        String[] headers = {"库分类", "药物分类", "药物名称", "药物描述"};

        // 创建 CSV 格式化器
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(fileName));
             CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(headers))) {

            // 循环遍历 CsvBo 列表，将每个对象的数据写入 CSV 文件
            for (CsvBo csvBo : dataList) {
                csvPrinter.printRecord(csvBo.getDatabase(), csvBo.getMedType(), csvBo.getMedName(), csvBo.getContent());
            }

            System.out.println("CSV 文件已生成: " + fileName);
        }
    }

    @SneakyThrows
    public static List<CsvBo> fetchInfo(List<BasicInfo> infoList,String medType){
        // 创建线程池，数量为可用核心数
        int threadCount = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

        // 用于存储所有的 Future 对象
        List<Future<CsvBo>> futures = new ArrayList<>();

        // 使用线程池异步处理 infoList 中的每个 BasicInfo 对象
        for (BasicInfo info : infoList) {
            // 提交每个任务到线程池
            futures.add(executorService.submit(new Callable<CsvBo>() {
                @Override
                public CsvBo call() throws Exception {
                    String infoUrl = info.getUrl();
                    Document infoDocument = getDocument(infoUrl);
                    String content = infoDocument
                            .select("div.w-950 > div.w-950.bg_white.zoom > div.l.w-710.o-v > div.content_1.wordcut > div.content_topp")
                            .text().replaceAll("sstp上海科学技术出版社nihao", "");

                    CsvBo csvBo = new CsvBo();
                    csvBo.setDatabase(DATABASE);
                    csvBo.setMedType(medType);
                    csvBo.setMedName(info.getName());
                    csvBo.setContent(content);
                    System.out.println(csvBo);
                    return csvBo;
                }
            }));
        }

        // 收集所有的结果
        List<CsvBo> resultList = new ArrayList<>();
        for (Future<CsvBo> future : futures) {
            // 阻塞直到任务完成，并获取结果
            CsvBo csvBo = future.get();
            resultList.add(csvBo);
        }

        // 关闭线程池
        executorService.shutdown();

        return resultList;
    }
}
