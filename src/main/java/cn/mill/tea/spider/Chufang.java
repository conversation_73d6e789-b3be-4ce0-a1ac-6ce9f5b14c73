package cn.mill.tea.spider;

import com.alibaba.fastjson2.JSON;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public class Chufang {

    private static final String DATABASE = "处方库";

    @SneakyThrows
    public static void main(String[] args) {
        String baseUrl = "http://tcm.sstp.cn/";
        String cataUrl = "http://tcm.sstp.cn/index.php?categorylist#chufang";
        Document cata = Jsoup.connect(cataUrl).get();
        Elements links = cata.select("ul.chufang > li > a");
        List<BasicInfo> cataData = new ArrayList<>();
        for (Element link : links) {
            String href = link.attr("href");
            String text = link.text().replaceAll(" ","");

            if (!href.isEmpty() && !text.isEmpty()) {
                BasicInfo data = new BasicInfo();
                data.setUrl(baseUrl + href);
                data.setName(text);
                cataData.add(data);
            }
        }

        int startPage = 1;
        List<CsvBo> result = new ArrayList<>();
        for (BasicInfo data : cataData) {
            String url = data.getUrl();
            Document document = Jsoup.connect(url).get();
            String maxPage;
            String fenye = Optional.ofNullable(document.getElementById("fenye"))
                    .map(element -> element.select("a"))
                    .map(Elements::last)
                    .map(Element::text)
                    .orElse(null);
            if (fenye == null) {
                maxPage = "1";
            } else {
                if (fenye.contains("...")) {
                    maxPage = fenye.replace("... ", "");
                } else {
                    Elements fenyeElement = document.getElementById("fenye").select("a");
                    maxPage = fenyeElement.get(fenyeElement.size() - 2).text();
                }
            }

            // 页数
            List<Integer> list = IntStream.rangeClosed(startPage, Integer.parseInt(maxPage)).boxed().collect(Collectors.toList());
            // 详细信息
            List<BasicInfo> infoList = new ArrayList<>();
            for (int i : list) {
                String listUrl = data.getUrl() + "-" + i;
                Document listDocument = Jsoup.connect(listUrl).get();
                Elements select = listDocument.select("dt.h2 > a");
                for (Element element : select) {
                    String href = element.attr("href");
                    String text = element.text();
                    if (!href.isEmpty() && !text.isEmpty()) {
                        BasicInfo info = new BasicInfo();
                        info.setUrl(baseUrl + href);
                        info.setName(text);
                        infoList.add(info);
                    }
                }
            }

            result.addAll(fetchInfo(infoList,data.getName()));

        }

        writeCsv(result, "处方库.csv");


        System.out.println(1);
    }

    @SneakyThrows
    private static Document getDocument(String url) {
        return Jsoup.connect(url)
                .cookie("hd_sid", AuthConstant.HD_SID)
                .cookie("hd_auth", AuthConstant.HD_AUTH)
                .get();
    }


    @SneakyThrows
    public static void writeCsv(List<CsvBo> dataList, String fileName) {
        //表头
        String[] headers = {"库分类", "药物分类", "药物名称", "药物描述"};

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(fileName));
             CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(headers))) {

            // 写入CSV
            for (CsvBo csvBo : dataList) {
                csvPrinter.printRecord(csvBo.getDatabase(), csvBo.getMedType(), csvBo.getMedName(), csvBo.getContent());
            }

            System.out.println("CSV 文件已生成: " + fileName);
        }
    }

    @SneakyThrows
    public static List<CsvBo> fetchInfo(List<BasicInfo> infoList,String medType){
        int threadCount = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

        List<Future<CsvBo>> futures = new ArrayList<>();

        for (BasicInfo info : infoList) {
            futures.add(executorService.submit(() -> {
                String infoUrl = info.getUrl();
                Document infoDocument = getDocument(infoUrl);
                String content = infoDocument
                        .select("div.w-950 > " +
                                "div.w-950.bg_white.zoom " +
                                "> div.l.w-710.o-v > " +
                                "div.content_1.wordcut > " +
                                "div.content_topp")
                        .text();

                CsvBo csvBo = new CsvBo();
                csvBo.setDatabase(DATABASE);
                csvBo.setMedType(medType);
                csvBo.setMedName(info.getName());
                csvBo.setContent(content);
                log.info(JSON.toJSONString(csvBo));
                return csvBo;
            }));
        }

         List<CsvBo> resultList = new ArrayList<>();
        for (Future<CsvBo> future : futures) {
            CsvBo csvBo = future.get();
            resultList.add(csvBo);
        }

        executorService.shutdown();

        return resultList;
    }
}