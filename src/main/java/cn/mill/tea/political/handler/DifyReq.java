package cn.mill.tea.political.handler;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.mill.tea.political.entity.Article;
import cn.mill.tea.political.mapper.ArticleMapper;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Component
public class DifyReq implements ApplicationRunner {
    @Resource
    ArticleMapper articleMapper;

    public static String API_KEY = "dataset-7mJH0cSKvGkwcTN4c7sJlNE4";

    public static String API_URL_QU = "http://dify.lijiaoyun.cn/v1/datasets/68354b45-6933-4757-9378-98370e507814/document/create-by-text";

    public static String API_URL_DS = "http://dify.lijiaoyun.cn/v1/datasets/3fafc68b-3cf5-4215-83d7-63e23267cae1/document/create-by-text";

    public static String API_URL_SH = "http://dify.lijiaoyun.cn/v1/datasets/6974c99e-65b4-490a-aa2c-8dd7a7520759/document/create-by-text";

    public static String API_URL_GJ = "http://dify.lijiaoyun.cn/v1/datasets/b6230888-a822-4cf4-8a51-7fa0d5e0766e/document/create-by-text";

    //2211
    //10711
    //14211
    //14270
    //23750
    //25136
    public static Integer ID = 25136;
    @Override
    public void run(ApplicationArguments args) throws Exception {
//       handle();
    }

    public void handle(){
        List<Article> articles = articleMapper.selectList(new QueryWrapper<Article>().gt("id", ID));
        int threadCount = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount*2);
        for (Article article : articles) {
            executorService.submit(()->{
                HttpResponse res = HttpUtil.createPost(API_URL_SH).header("Authorization", "Bearer " + API_KEY)
                        .body(JSON.toJSONString(article)).execute();
                log.info("处理完成：{}", res.body());
            });
        }
        executorService.shutdown();
        log.info("全部处理完毕");
    }

}