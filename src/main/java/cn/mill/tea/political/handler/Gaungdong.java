package cn.mill.tea.political.handler;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import cn.mill.tea.political.entity.Article;
import cn.mill.tea.political.mapper.ArticleMapper;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component
@Slf4j
public class Gaungdong implements ApplicationRunner {

    @Resource
    ArticleMapper articleMapper;

    private final int threadCount = Runtime.getRuntime().availableProcessors();
    ExecutorService executorService = Executors.newFixedThreadPool(threadCount * 2);

    @Override
    public void run(ApplicationArguments args) throws Exception {
//        handle();
//        handle1();
//        handle2();
    }

    @SneakyThrows
    //国家制度文件 + 广东省制度文件
    public void handle() {
        List<String> pages = new ArrayList<>();
        pages.add("http://www.gd.gov.cn/zwgk/zfxxgkzdwj/gjzdwj/index.html");
        pages.add("http://www.gd.gov.cn/zwgk/zfxxgkzdwj/gjzdwj/index_2.html");
        pages.add("http://www.gd.gov.cn/zwgk/zfxxgkzdwj/gjzdwj/index_3.html");
        pages.add("http://www.gd.gov.cn/zwgk/zfxxgkzdwj/gdszdwj/index.html");
        pages.add("http://www.gd.gov.cn/zwgk/zfxxgkzdwj/gdszdwj/index_2.html");

        List<String> urls = new ArrayList<>();
//        for (String page : pages) {
//            Document doc = Jsoup.connect(page).get();
//            Elements elements = doc.select("div.viewlist ul li span.til a");
//            elements.forEach(e->urls.add(e.attr("href")));
//        }

        List<Article> articleList = new ArrayList<>();
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/146/post_146885.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/146/post_146887.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content//0/145/post_145968.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/146/post_146204.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/146/post_146007.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/145/post_145656.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/145/post_145368.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/145/post_145369.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/144/post_144996.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/144/post_144879.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/143/post_143804.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/139/post_139641.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/142/post_142767.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/144/post_144006.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/142/post_142960.html");
        urls.add("https://www.gd.gov.cn/gkmlpt/content/0/142/post_142312.html");
        for (String url : urls) {
            if (url.contains("gd.gov")) {
                Document doc = Jsoup.connect(url).get();
                log.info("url：{}", url);
                String title = doc.select("div.content h1.title document-number").text();
                String content = doc.select("div.article-content").text();
                articleList.add(Article.init().setUrl(url).setText(content).setName(title));
            }
        }

        articleMapper.batchInsert(articleList);
    }


    //https://edu.gd.gov.cn/gkmlpt/**
    public void handle1() {
        List<String> urlList = new ArrayList<>();
        for (int i = 0; i < 1; i++) {
            HttpRequest req = new HttpRequest(UrlBuilder.of("https://edu.gd.gov.cn/gkmlpt/api/all/1661")).method(Method.GET)
                    .form("sid", "168");
            String res = req.form("page", i + 1).execute().body();
            JSONObject obj = JSON.parseObject(res);
            JSONArray articles = obj.getJSONArray("articles");
            for (int j = 0; j < articles.size(); j++) {
                JSONObject article = (JSONObject) articles.get(j);
                String url = article.getString("url");
                if (Objects.nonNull(url)) {
                    log.info("url：{}", url);
                    urlList.add(url);
                }
            }
        }
        for (int i = 0; i < 12; i++) {
            HttpRequest req = new HttpRequest(UrlBuilder.of("https://edu.gd.gov.cn/gkmlpt/api/all/1620")).method(Method.GET)
                    .form("sid", "168");
            String res = req.form("page", i + 1).execute().body();
            JSONObject obj = JSON.parseObject(res);
            JSONArray articles = obj.getJSONArray("articles");
            for (int j = 0; j < articles.size(); j++) {
                JSONObject article = (JSONObject) articles.get(j);
                String url = article.getString("url");
                if (Objects.nonNull(url)) {
                    log.info("url：{}", url);
                    urlList.add(url);
                }
            }
        }
        for (int i = 0; i < 84; i++) {
            HttpRequest req = new HttpRequest(UrlBuilder.of("https://edu.gd.gov.cn/gkmlpt/api/all/0")).method(Method.GET)
                    .form("sid", "168");
            String res = req.form("page", i + 1).execute().body();
            JSONObject obj = JSON.parseObject(res);
            JSONArray articles = obj.getJSONArray("articles");
            for (int j = 0; j < articles.size(); j++) {
                JSONObject article = (JSONObject) articles.get(j);
                String url = article.getString("url");
                if (Objects.nonNull(url)) {
                    log.info("url：{}", url);
                    urlList.add(url);
                }
            }
        }
        List<Article> articleList = new ArrayList<>();
        int threadCount = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount * 2);
        List<Future<Article>> futures = new ArrayList<>();
        for (String url : urlList) {
            futures.add(executorService.submit(() -> {
                Document cata = Jsoup.connect(url).get();

                String title = Optional.ofNullable(cata.selectFirst("head title")).map(Element::text).orElse("");
                Element element = cata.selectFirst("div.content-box");

                if (StrUtil.isEmpty(title) && element != null) {
                    title = Optional.ofNullable(element.selectFirst("div.content h1.title.document-number")).map(Element::text).orElse("");
                }
                log.info("题目为：{}", title);
                element.select("div.op-row").remove();

                String content = element.text();
                return Article.init().setUrl(url).setText(content).setName(title);

            }));
        }
        for (Future<Article> future : futures) {
            try {
                articleList.add(future.get());
            } catch (Exception e) {
                log.error("处理异常：", e);
            }
        }
        executorService.shutdown();
        int i1 = articleList.size() % 500;
        articleMapper.batchInsert(articleList.subList(0, i1));
        articleList = articleList.subList(i1, articleList.size());
        for (int i = 0; i < articleList.size() / 500; i++) {
            articleMapper.batchInsert(articleList.subList(i * 500, i * 500 + 500));
        }

    }

    //https://edu.gd.gov.cn/zwgknew/bmyjs/**
    @SneakyThrows
    public void handle2() {
        List<String> catas = new ArrayList<>();
        //部门预决算全是附件，先不爬取
//        catas.add("https://edu.gd.gov.cn/zwgknew/bmyjs/index.html");
        catas.add("https://edu.gd.gov.cn/zwgknew/gdjynj/index.html");
        //公式公告，包含附件
        catas.add("https://edu.gd.gov.cn/zwgknew/gsgg/index.html");
        //信息公开年度报告，全是附件
//        catas.add("https://edu.gd.gov.cn/zwgknew/gxxxgkzl/ndbg/index.html");
        catas.add("https://edu.gd.gov.cn/zwgknew/gxxxgkzl/xxgkgz/index.html");
        catas.add("https://edu.gd.gov.cn/zwgknew/jyfzgh/index.html");
        catas.add("https://edu.gd.gov.cn/zwgknew/jytagk/index.html");
        catas.add("https://edu.gd.gov.cn/zwgknew/xxgk/gknb/index.html");
        //政府网站年度报表，大多为图片表格
//        catas.add("https://edu.gd.gov.cn/zwgknew/xxgk/ndbb/index.html");
        //行政执法，包含附件
        catas.add("https://edu.gd.gov.cn/zwgknew/xzzfsgs/index.html");
        //招标采购，包含附件，会跳转到gdgpo.czt.gd.gov.cn “广东省政府采购网”
        catas.add("https://edu.gd.gov.cn/zwgknew/zbcg/index.html");
        //政策解读，多为长图，暂不处理
//        catas.add("https://edu.gd.gov.cn/zwgknew/zcjd/");

//        List<String> pages = new ArrayList<>();
//        for (String cata : catas) {
//            Document cataDoc = Jsoup.connect(cata).get();
//            Elements indexs = cataDoc.select("div.pages a");
//            String firstPage = indexs.get(0).attr("href");
//            String endPage = indexs.get(indexs.size() - 1).attr("href");
//            int i = endPage.indexOf("_");
//            if (i != -1) {
//                String fontUrl = endPage.substring(0, i + 1);
//                String backUrl = endPage.substring(endPage.indexOf("_") + 1);
//                String endUrl = ".html";
//                String maxIndex = backUrl.substring(0, backUrl.indexOf(".html"));
//                if (!"1".equals(maxIndex)) {
//                    List<Integer> list = IntStream.rangeClosed(2, Integer.parseInt(maxIndex)).boxed().collect(Collectors.toList());
//                    pages.add((fontUrl + endUrl).replace("_", ""));
//                    list.forEach(e -> pages.add(fontUrl + e + endUrl));
//                } else {
//                    pages.add(firstPage);
//                }
//            } else {
//                pages.add(firstPage);
//            }
//        }
//
//        //处理具体页面url
//        List<String> urls = new ArrayList<>();
//
//        for (String page : pages) {
//            Document document = Jsoup.connect(page).get();
//            Elements indexs = document.select("ul.list li a");
//            indexs.forEach(e -> {
//                String href = e.attr("href");
//                if (href.contains("edu.gd.gov")||href.contains("gdgpo.czt")) {
//                    log.info("文章链接：{}", href);
//                    urls.add(href);
//                }
//            });
//        }
//
//        String json = JSON.toJSONString(urls, JSONWriter.Feature.LargeObject);
//        new BufferedWriter(new FileWriter("./gd_urls.txt")).write(json);


        //TODO
        StringBuilder stringBuilder = new StringBuilder();

        String json;
        try (BufferedReader reader = new BufferedReader(new FileReader("./gd_urls.txt"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
            json = stringBuilder.toString();
        }
        List<String> urls;
        urls = JSON.parseArray(json, String.class);
        List<Future<Article>> futures = new ArrayList<>();
        for (String url : urls) {
            if (url.contains("edu.gd.gov") || url.contains("gdgpo.czt")) {
                futures.add(executorService.submit(() -> {
                    Thread.sleep(500);
                    log.info("处理url：{}", url);
                    Document document = Jsoup.connect(url).get();
                    if (url.contains("edu.gd.gov")) {
                        Element element = document.selectFirst("div.main");
                        Element titleEle = element.selectFirst("h1.content_title");
                        String title;
                        if (titleEle == null) {
                            title = element.selectFirst("div.con h3").text();

                        } else {
                            title = titleEle.text();

                        }
                        log.info("文章标题：{}", title);
                        element.select("div.content_ewm").remove();
                        String content = element.text();
                        return Article.init().setUrl(url).setName(title).setText(content);
                    } else {
                        Element element = document.selectFirst("div.al-main");
                        String title = element.selectFirst("p.info-title").text();
                        log.info("文章标题：{}", title);
                        element.select("div.content_ewm").remove();
                        String content = element.text();
                        return Article.init().setUrl(url).setName(title).setText(content);
                    }

                }));
            }
        }
        List<Article> articleList = new ArrayList<>();
        for (Future<Article> future : futures) {
            try {
                articleList.add(future.get());
            } catch (Exception e) {
                log.error("处理异常：", e);
            }
        }
        int i1 = articleList.size() % 500;
        articleMapper.batchInsert(articleList.subList(0, i1));
        articleList = articleList.subList(i1, articleList.size());
        for (int i = 0; i < articleList.size() / 500; i++) {
            articleMapper.batchInsert(articleList.subList(i * 500, i * 500 + 500));
        }
        log.info("处理完毕");

    }

    public static void main(String[] args) {
        List<String> urls = new ArrayList<>();
        urls.add("1");
        System.out.println(urls.subList(0, 1));
    }
}