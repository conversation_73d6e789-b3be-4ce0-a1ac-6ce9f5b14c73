package cn.mill.tea.political.handler;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import cn.mill.tea.political.entity.Article;
import cn.mill.tea.political.mapper.ArticleMapper;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.FileReader;
import java.net.HttpCookie;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Component
public class Guangzhou implements ApplicationRunner {

    @Resource
    ArticleMapper articleMapper;

    private final int threadCount = Runtime.getRuntime().availableProcessors();
    ExecutorService executorService = Executors.newFixedThreadPool(threadCount * 2);

    public static void main(String[] args) {
//        handle3();
    }


    @Override
    public void run(ApplicationArguments args) throws Exception {
//        handle();
//        handle2();
//        handle3();
    }

    //政策+工作年度报告
    @SneakyThrows
    public void handle() {
        HttpCookie cookie = new HttpCookie("UqZBpD3n3iXPAw1X", "v1TS-sgwSD1Wy");
        cookie.setPath("/");
        cookie.setMaxAge(0);
        cookie.setDomain("www.panyu.gov.cn");

        List<String> apiList = new ArrayList<>();
        //政策
        apiList.add("https://jyj.gz.gov.cn/gkmlpt/api/all/16148?page=1&sid=200016");
        //工作年度报告
        apiList.add("https://jyj.gz.gov.cn/gkmlpt/api/all/251?page=1&sid=200016");
        //

        List<String> urlList = new ArrayList<>();

        for (String api : apiList) {
            HttpRequest req = new HttpRequest(UrlBuilder.of(api)).method(Method.GET)
                    .form("sid", "200404")
                    .cookie("Path=/; Path=/; Path=/; common-secure; Path=/; UqZBpD3n3iXPAw1X=v1TS-sgwSD1Wy; laravel_session=eyJpdiI6ImRLUGN0XC9pTU14VSt5Y2tFS2ZLXC9vZz09IiwidmFsdWUiOiIyblo5S1hjMERWVklBc2R6Smc3ZzJkYVBXUWFtRTJMcEZvQmVFc0MyK203REkxc2Z4bWs1M3ZFRFwvbnZCSWxCWSIsIm1hYyI6ImUwYjkwMjY4ODJlNGJkZDBhYjg4NWExNjEyZDViYzIyMDQwZTJmYWY0YzAzMTI5OGEyZmY5NTBmYzBmNzc2Y2EifQ%3D%3D; front_uc_session=***************************************************************************************************************************************************************************************************************************************************%3D");
            String res = req.execute().body();
            JSONObject obj = JSON.parseObject(res);
            JSONArray articles = obj.getJSONArray("articles");
            for (int j = 0; j < articles.size(); j++) {
                JSONObject article = (JSONObject) articles.get(j);
                String url = article.getString("url");
                if (Objects.nonNull(url)) {
                    log.info("url：{}", url);
                    urlList.add(url);
                }
            }
        }

        List<Article> articleList = new ArrayList<>();
        List<Future<Article>> futures = new ArrayList<>();
        for (String url : urlList) {
            futures.add(executorService.submit(() -> {
                Document cata = Jsoup.connect(url).get();
                String title = Optional.ofNullable(cata.selectFirst("head title")).map(Element::text).orElse("");
                Element element = cata.selectFirst("div.content-box");

                if (StrUtil.isEmpty(title) && element != null) {
                    title = Optional.ofNullable(element.selectFirst("div.content h1.title.document-number")).map(Element::text).orElse("");
                }
                log.info("题目为：{}", title);
                element.select("div.op-row").remove();

                String content = element.text();
                return Article.init().setUrl(url).setText(content).setName(title);
            }));
        }
        for (Future<Article> future : futures) {
            try {
                articleList.add(future.get());
            } catch (Exception e) {
                log.error("处理异常：", e);
            }
        }
        articleMapper.batchInsert(articleList);
    }

    //法定主动公开内容
    @SneakyThrows
    public void handle2() {
        List<String> urlList = new ArrayList<>();
        for (int i = 35; i < 100; i++) {
            Thread.sleep(1000);
            HttpRequest req = new HttpRequest(UrlBuilder.of("https://jyj.gz.gov.cn/gkmlpt/api/all/0")).method(Method.GET)
                    .form("sid", "200404");
            String res = req.form("page", i + 1).execute().body();
            JSONObject obj = JSON.parseObject(res);
            JSONArray articles = obj.getJSONArray("articles");
            for (int j = 0; j < articles.size(); j++) {
                JSONObject article = (JSONObject) articles.get(j);
                String url = article.getString("url");
                if (Objects.nonNull(url)) {
                    log.info("url：{}", url);
                    urlList.add(url);
                }
            }
        }
        List<Article> articleList = new ArrayList<>();
        int threadCount = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        List<Future<Article>> futures = new ArrayList<>();
        for (String url : urlList) {
            if (url.contains("jyj.gz.gov.cn")) {
                futures.add(executorService.submit(() -> {
                    Document cata = Jsoup.connect(url).get();

                    String title = Optional.ofNullable(cata.selectFirst("head title")).map(Element::text).orElse("");
                    Element element = cata.selectFirst("div.content-box");

                    if (StrUtil.isEmpty(title) && element != null) {
                        title = Optional.ofNullable(element.selectFirst("div.content h1.title.document-number")).map(Element::text).orElse("");
                    }
                    log.info("题目为：{}", title);
                    element.select("div.op-row").remove();

                    String content = element.text();
                    return Article.init().setUrl(url).setText(content).setName(title);

                }));
            }

        }
        for (Future<Article> future : futures) {
            try {
                articleList.add(future.get());
            } catch (Exception e) {
                log.error("处理异常：", e);
            }
        }
        executorService.shutdown();
        int size = articleList.size();
        for (int i = 1; i < articleList.size() / 500; i++) {
            articleMapper.batchInsert(articleList.subList(i * 500, i * 500 + 500));
        }
//        articleMapper.batchInsert(articleList);
    }

    //其他静态栏目
    @SneakyThrows
    public void handle3() {
        List<String> catas = new ArrayList<>();
        catas.add("https://jyj.gz.gov.cn/zt/shuangjian/");
        catas.add("https://jyj.gz.gov.cn/zt/gzjyky/");
        catas.add("https://jyj.gz.gov.cn/zt/gzxqjyfzgc/");
        catas.add("https://jyj.gz.gov.cn/zt/fzzfjs/");
        catas.add("https://jyj.gz.gov.cn/zt/gxbysjyfw/");
        catas.add("https://jyj.gz.gov.cn/zt/bfgz/");
        catas.add("https://jyj.gz.gov.cn/yw2/jyfw/xqjy/index.html");
        catas.add("https://jyj.gz.gov.cn/yw2/jyfw/ywjy/");
        catas.add("https://jyj.gz.gov.cn/yw2/jyfw/gzjy/");
        catas.add("https://jyj.gz.gov.cn/yw2/jyfw/gdjy/");
        catas.add("https://jyj.gz.gov.cn/yw2/jyfw/tsjy/");
        catas.add("https://jyj.gz.gov.cn/yw2/jyfw/zyjy/");
        catas.add("https://jyj.gz.gov.cn/yw/jyyw/");
        catas.add("https://jyj.gz.gov.cn/yw/tzgg/");
        catas.add("https://jyj.gz.gov.cn/yw/tpxw/");
        catas.add("https://jyj.gz.gov.cn/yw/wsgs/");
        catas.add("https://jyj.gz.gov.cn/yw/zcjd/");
        catas.add("https://jyj.gz.gov.cn/yw/jysj/");
        catas.add("https://jyj.gz.gov.cn/yw/ssgxdt/");
        catas.add("https://jyj.gz.gov.cn/yw/qjyjdt/");
        catas.add("https://jyj.gz.gov.cn/yw/jyxtdt/");
        catas.add("https://jyj.gz.gov.cn/yw/zsks/");
        catas.add("https://jyj.gz.gov.cn/yw/jyxmt/");

//        List<String> pages = new ArrayList<>();
//        for (String cata : catas) {
//            Document cataDoc = Jsoup.connect(cata).get();
//            Elements indexs = cataDoc.select("div.pagediv span a");
//            String firstPage = indexs.get(0).attr("href");
//            String endPage = indexs.get(indexs.size() - 1).attr("href");
//            int i = endPage.indexOf("_");
//            if (i != -1) {
//                String fontUrl = endPage.substring(0, i + 1);
//                String backUrl = endPage.substring(endPage.indexOf("_") + 1);
//                String endUrl = ".html";
//                String maxIndex = backUrl.substring(0, backUrl.indexOf(".html"));
//                if (!"1".equals(maxIndex)) {
//                    List<Integer> list = IntStream.rangeClosed(2, Integer.parseInt(maxIndex)).boxed().collect(Collectors.toList());
//                    pages.add((fontUrl + endUrl).replace("_", ""));
//                    list.forEach(e -> pages.add(fontUrl + e + endUrl));
//                } else {
//                    pages.add(firstPage);
//                }
//            } else {
//                pages.add(firstPage);
//            }
//        }

        StringBuilder stringBuilder = new StringBuilder();

        String json;
        try (BufferedReader reader = new BufferedReader(new FileReader("./urls.txt"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
            json = stringBuilder.toString();
        }

        //处理具体页面url
        List<String> urls = new ArrayList<>();

//        for (String page : pages) {
//            Document document = Jsoup.connect(page).get();
//            Elements indexs = document.select("div.news_list ul li a");
//            indexs.forEach(e -> {
//                String href = e.attr("href");
//                if (href.contains("jyj.gz.gov")) {
//                    log.info("文章链接：{}", href);
//                    urls.add(href);
//                }
//            });
//        }
        urls = JSON.parseArray(json, String.class);
        List<Article> articleList = new ArrayList<>();
        List<Future<Article>> futures = new ArrayList<>();
        for (String url : urls) {
            futures.add(executorService.submit(() -> {
                Thread.sleep(500);
                Document document = Jsoup.connect(url).get();
                Element element = document.selectFirst("div.main");
                String title = element.selectFirst("h1.content_title").text();
                log.info("文章标题：{}", title);
                element.select("div.content_ewm").remove();
                String content = element.text();
                return Article.init().setUrl(url).setName(title).setText(content);
            }));
        }
        for (Future<Article> future : futures) {
            try {
                articleList.add(future.get());
            } catch (Exception e) {
                log.error("处理异常：", e);
            }
        }
        for (int i = 1; i < articleList.size() / 500; i++) {
            articleMapper.batchInsert(articleList.subList(i * 500, i * 500 + 500));
        }


    }

    @SneakyThrows
    public void handle4() {

    }

}