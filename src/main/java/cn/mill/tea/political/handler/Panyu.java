package cn.mill.tea.political.handler;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import cn.mill.tea.political.entity.Article;
import cn.mill.tea.political.mapper.ArticleMapper;
import cn.mill.tea.spider.CsvBo;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.HttpCookie;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * 番禺区
 */
@Component
@Slf4j
public class Panyu implements ApplicationRunner {

    @Resource
    ArticleMapper articleMapper;

    @SneakyThrows
    public static void main(String[] args) {
        new Panyu().handle();
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
//        handle2();
    }

    //法定主动公开内容
    @SneakyThrows
    public void handle() {
        //2067条->每次返回100条，共21页
        HttpCookie cookie = new HttpCookie("UqZBpD3n3iXPAw1X", "v1TS-sgwSD1Wy");
        cookie.setPath("/");
        cookie.setMaxAge(0);
        cookie.setDomain("www.panyu.gov.cn");

//        HttpRequest req = new HttpRequest(UrlBuilder.of("https://www.panyu.gov.cn/gzpyjy/gkmlpt/api/all/0")).method(Method.GET)
//                .form("sid", "200404").cookie(cookie);
        List<String> urlList = new ArrayList<>();
        for (int i = 0; i < 21; i++) {
            HttpRequest req = new HttpRequest(UrlBuilder.of("https://www.panyu.gov.cn/gzpyjy/gkmlpt/api/all/0")).method(Method.GET)
                    .form("sid", "200404")
                    .cookie("Path=/; Path=/; Path=/; common-secure; Path=/; UqZBpD3n3iXPAw1X=v1TS-sgwSD1Wy; laravel_session=eyJpdiI6ImRLUGN0XC9pTU14VSt5Y2tFS2ZLXC9vZz09IiwidmFsdWUiOiIyblo5S1hjMERWVklBc2R6Smc3ZzJkYVBXUWFtRTJMcEZvQmVFc0MyK203REkxc2Z4bWs1M3ZFRFwvbnZCSWxCWSIsIm1hYyI6ImUwYjkwMjY4ODJlNGJkZDBhYjg4NWExNjEyZDViYzIyMDQwZTJmYWY0YzAzMTI5OGEyZmY5NTBmYzBmNzc2Y2EifQ%3D%3D; front_uc_session=***************************************************************************************************************************************************************************************************************************************************%3D");
            String res = req.form("page", i + 1).execute().body();
            JSONObject obj = JSON.parseObject(res);
            JSONArray articles = obj.getJSONArray("articles");
            for (int j = 0; j < articles.size(); j++) {
                JSONObject article = (JSONObject) articles.get(j);
                String url = article.getString("url");
                if (Objects.nonNull(url)) {
                    log.info("url：{}", url);
                    urlList.add(url);
                }
            }
        }
        List<Article> articleList = new ArrayList<>();
        int threadCount = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        List<Future<Article>> futures = new ArrayList<>();
        for (String url : urlList) {
            futures.add(executorService.submit(() -> {
                Document cata = Jsoup.connect(url).get();
                if (url.contains("sfj.gz.gov.cn")) {
                    String title = Optional.ofNullable(cata.selectFirst("head title")).map(Element::text).orElse("");
                    Element element = cata.selectFirst("div.article_content");
                    if (StrUtil.isEmpty(title) && element != null) {
                        title = Optional.ofNullable(element.selectFirst("p")).map(Element::text).orElse("");
                    }
                    log.info("题目为：{}", title);
                    element.select("div.article_tips").remove();
                    String content = element.text();
                    return Article.init().setUrl(url).setText(content).setName(title);
                } else {
                    String title = Optional.ofNullable(cata.selectFirst("head title")).map(Element::text).orElse("");
                    Element element = cata.selectFirst("div.content-box");

                    if (StrUtil.isEmpty(title) && element != null) {
                        title = Optional.ofNullable(element.selectFirst("div.content h1.title.document-number")).map(Element::text).orElse("");
                    }
                    log.info("题目为：{}", title);
                    element.select("div.op-row").remove();

                    String content = element.text();
                    return Article.init().setUrl(url).setText(content).setName(title);
                }
            }));
        }
        for (Future<Article> future : futures) {
            try {
                articleList.add(future.get());
            } catch (Exception e) {
                log.error("处理异常：", e);
            }
        }
        executorService.shutdown();
        articleMapper.batchInsert(articleList);
    }

    //年度报告
    @SneakyThrows
    public void handle2() {
        String baseUrl = "http://www.panyu.gov.cn/gzpyjy/gkmlpt/api/all/1334?page=1&sid=200404";
        HttpRequest req = new HttpRequest(UrlBuilder.of(baseUrl)).method(Method.GET)
                .header("Cookie", "Path=/; Path=/; Path=/; common-secure; Path=/; UqZBpD3n3iXPAw1X=v1TS-sgwSD1Wy; laravel_session=eyJpdiI6ImRLUGN0XC9pTU14VSt5Y2tFS2ZLXC9vZz09IiwidmFsdWUiOiIyblo5S1hjMERWVklBc2R6Smc3ZzJkYVBXUWFtRTJMcEZvQmVFc0MyK203REkxc2Z4bWs1M3ZFRFwvbnZCSWxCWSIsIm1hYyI6ImUwYjkwMjY4ODJlNGJkZDBhYjg4NWExNjEyZDViYzIyMDQwZTJmYWY0YzAzMTI5OGEyZmY5NTBmYzBmNzc2Y2EifQ%3D%3D; front_uc_session=***************************************************************************************************************************************************************************************************************************************************%3D");
        JSONObject obj = JSON.parseObject(req.execute().body());
        JSONArray articles = obj.getJSONArray("articles");
        List<String> urlList = new ArrayList<>();
        for (int j = 0; j < articles.size(); j++) {
            JSONObject article = (JSONObject) articles.get(j);
            String url = article.getString("url");
            if (Objects.nonNull(url)) {
                log.info("url：{}", url);
                urlList.add(url);
            }
        }
        List<Article> articleList = new ArrayList<>();
        for (String url : urlList) {
            Document cata = Jsoup.connect(url).get();
            String title = Optional.ofNullable(cata.selectFirst("head title")).map(Element::text).orElse("");
            Element element = cata.selectFirst("div.content-box");

            if (StrUtil.isEmpty(title) && element != null) {
                title = Optional.ofNullable(element.selectFirst("div.content h1.title.document-number")).map(Element::text).orElse("");
            }
            log.info("题目为：{}", title);
            element.select("div.op-row").remove();

            String content = element.text();
            articleList.add(Article.init().setUrl(url).setText(content).setName(title));
        }
        articleMapper.batchInsert(articleList);
    }
}