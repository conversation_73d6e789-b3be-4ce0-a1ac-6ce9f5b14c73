package cn.mill.tea.political.handler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

public class <PERSON>ji<PERSON> {
    public void handle(){
        
    }

    @Data
    @Accessors(chain = true)
    public class Leader{
        String name;
        @JSONField(name = "org_name")
        String orgName;
        String phone;
    }

    @SneakyThrows
    public static void main(String[] args) {
        String s = new String(Files.readAllBytes(Paths.get("leaders.json")));
        List<List<Leader>> lists = JSON.parseObject(s, new TypeReference<List<List<Leader>>>() {});

        StringBuilder res =  new StringBuilder();

        for (List<Leader> list : lists) {
            for (Leader leader : list) {
                res.append(leader.getName()).append("\t");
                res.append(leader.getOrgName()).append("\t");
                res.append(leader.getPhone()).append("\t");
            }
            res.append("\n");
        }

        System.out.println(res);
    }


}