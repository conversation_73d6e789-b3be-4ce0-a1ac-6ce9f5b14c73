package cn.mill.tea.political.entity;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.PropertyNamingStrategy;
import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@TableName(value = "article",autoResultMap = true)
@Data
@Accessors(chain = true)
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class Article {
    private Integer id;
    private String url;
    private String name;
    private String text;
    private String indexingTechnique;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ProcessRule processRule;

    @Data
    @Accessors(chain = true)
    public static class ProcessRule{
        private String mode = "automatic";
    }


    public static Article init(){
        return new Article().setProcessRule(new ProcessRule().setMode("automatic"));
    }

}